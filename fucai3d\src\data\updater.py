"""
增量更新器模块
实现增量更新机制，避免重复采集，提高效率
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import time

logger = logging.getLogger(__name__)


class IncrementalUpdater:
    """增量更新器"""

    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.data_sources = {
            'primary': "https://data.17500.cn/3d_desc.txt",  # 新的完整数据源
            'backup': "https://data.17500.cn/3d_asc.txt"     # 备用数据源
        }
        self.update_stats = {
            'last_update_time': None,
            'total_updates': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0
        }
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_latest_issue(self) -> Optional[str]:
        """获取数据库中最新的期号"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result['issue'] if result else None
        except Exception as e:
            logger.error(f"获取最新期号失败: {e}")
            return None
    
    def get_latest_date(self) -> Optional[str]:
        """获取数据库中最新的开奖日期"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT draw_date FROM lottery_data ORDER BY draw_date DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result['draw_date'] if result else None
        except Exception as e:
            logger.error(f"获取最新日期失败: {e}")
            return None
    
    def get_missing_issues(self, start_issue: str, end_issue: str) -> List[str]:
        """获取缺失的期号列表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data WHERE issue BETWEEN ? AND ? ORDER BY issue",
                    (start_issue, end_issue)
                )
                existing_issues = {row['issue'] for row in cursor.fetchall()}
            
            # 生成期号范围
            start_num = int(start_issue)
            end_num = int(end_issue)
            all_issues = {str(i).zfill(7) for i in range(start_num, end_num + 1)}
            
            # 找出缺失的期号
            missing_issues = sorted(all_issues - existing_issues)
            return missing_issues
            
        except Exception as e:
            logger.error(f"获取缺失期号失败: {e}")
            return []
    
    def check_record_exists(self, issue: str) -> bool:
        """检查记录是否已存在"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT 1 FROM lottery_data WHERE issue = ? LIMIT 1",
                    (issue,)
                )
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"检查记录存在性失败: {e}")
            return False
    
    def get_record_by_issue(self, issue: str) -> Optional[Dict[str, Any]]:
        """根据期号获取记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM lottery_data WHERE issue = ?",
                    (issue,)
                )
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.error(f"获取记录失败: {e}")
            return None
    
    def insert_new_record(self, record: Dict[str, Any]) -> bool:
        """插入新记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
                 machine_number, sales_amount, prize_info, sum_value, span, number_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    record.get('issue'),
                    record.get('draw_date'),
                    record.get('hundreds'),
                    record.get('tens'),
                    record.get('units'),
                    record.get('trial_hundreds'),
                    record.get('trial_tens'),
                    record.get('trial_units'),
                    record.get('machine_number'),
                    record.get('sales_amount'),
                    record.get('prize_info'),
                    record.get('sum_value'),
                    record.get('span'),
                    record.get('number_type')
                ))
                conn.commit()
                self.update_stats['new_records'] += 1
                return True
        except Exception as e:
            logger.error(f"插入新记录失败: {e}")
            self.update_stats['error_records'] += 1
            return False
    
    def update_existing_record(self, record: Dict[str, Any]) -> bool:
        """更新现有记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                UPDATE lottery_data SET
                    draw_date = ?, hundreds = ?, tens = ?, units = ?,
                    trial_hundreds = ?, trial_tens = ?, trial_units = ?,
                    machine_number = ?, sales_amount = ?, prize_info = ?,
                    sum_value = ?, span = ?, number_type = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE issue = ?
                """
                conn.execute(sql, (
                    record.get('draw_date'),
                    record.get('hundreds'),
                    record.get('tens'),
                    record.get('units'),
                    record.get('trial_hundreds'),
                    record.get('trial_tens'),
                    record.get('trial_units'),
                    record.get('machine_number'),
                    record.get('sales_amount'),
                    record.get('prize_info'),
                    record.get('sum_value'),
                    record.get('span'),
                    record.get('number_type'),
                    record.get('issue')
                ))
                conn.commit()
                self.update_stats['updated_records'] += 1
                return True
        except Exception as e:
            logger.error(f"更新记录失败: {e}")
            self.update_stats['error_records'] += 1
            return False
    
    def is_record_different(self, new_record: Dict[str, Any], existing_record: Dict[str, Any]) -> bool:
        """检查记录是否有差异"""
        # 比较关键字段
        key_fields = [
            'draw_date', 'hundreds', 'tens', 'units',
            'trial_hundreds', 'trial_tens', 'trial_units',
            'machine_number', 'sales_amount', 'prize_info'
        ]
        
        for field in key_fields:
            new_value = new_record.get(field)
            existing_value = existing_record.get(field)
            
            # 处理None值比较
            if new_value != existing_value:
                # 特殊处理数值类型
                if isinstance(new_value, (int, float)) and isinstance(existing_value, (int, float)):
                    if abs(new_value - existing_value) > 0.001:  # 浮点数比较
                        return True
                else:
                    return True
        
        return False
    
    def process_incremental_update(self, new_records: List[Dict[str, Any]], 
                                 force_update: bool = False) -> Dict[str, Any]:
        """处理增量更新"""
        start_time = time.time()
        logger.info(f"开始增量更新，共 {len(new_records)} 条记录")
        
        update_summary = {
            'total_processed': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0,
            'processing_time': 0,
            'details': []
        }
        
        for record in new_records:
            self.update_stats['total_updates'] += 1
            update_summary['total_processed'] += 1
            
            issue = record.get('issue')
            if not issue:
                logger.warning("记录缺少期号，跳过")
                update_summary['error_records'] += 1
                continue
            
            try:
                existing_record = self.get_record_by_issue(issue)
                
                if existing_record is None:
                    # 新记录
                    if self.insert_new_record(record):
                        update_summary['new_records'] += 1
                        logger.info(f"插入新记录: {issue}")
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'inserted',
                            'status': 'success'
                        })
                    else:
                        update_summary['error_records'] += 1
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'insert',
                            'status': 'failed'
                        })
                else:
                    # 检查是否需要更新
                    if force_update or self.is_record_different(record, existing_record):
                        if self.update_existing_record(record):
                            update_summary['updated_records'] += 1
                            logger.info(f"更新记录: {issue}")
                            update_summary['details'].append({
                                'issue': issue,
                                'action': 'updated',
                                'status': 'success'
                            })
                        else:
                            update_summary['error_records'] += 1
                            update_summary['details'].append({
                                'issue': issue,
                                'action': 'update',
                                'status': 'failed'
                            })
                    else:
                        # 记录相同，跳过
                        update_summary['skipped_records'] += 1
                        self.update_stats['skipped_records'] += 1
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'skipped',
                            'status': 'no_change'
                        })
            
            except Exception as e:
                logger.error(f"处理记录 {issue} 时出错: {e}")
                update_summary['error_records'] += 1
                update_summary['details'].append({
                    'issue': issue,
                    'action': 'error',
                    'status': 'failed',
                    'error': str(e)
                })
        
        # 更新统计信息
        processing_time = time.time() - start_time
        update_summary['processing_time'] = processing_time
        self.update_stats['last_update_time'] = datetime.now().isoformat()
        
        logger.info(f"增量更新完成:")
        logger.info(f"  处理时间: {processing_time:.2f} 秒")
        logger.info(f"  总处理数: {update_summary['total_processed']}")
        logger.info(f"  新增记录: {update_summary['new_records']}")
        logger.info(f"  更新记录: {update_summary['updated_records']}")
        logger.info(f"  跳过记录: {update_summary['skipped_records']}")
        logger.info(f"  错误记录: {update_summary['error_records']}")
        
        return update_summary
    
    def get_update_stats(self) -> Dict[str, Any]:
        """获取更新统计信息"""
        return self.update_stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.update_stats = {
            'last_update_time': None,
            'total_updates': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0
        }


class DataSynchronizer:
    """数据同步器 - 确保数据一致性"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.updater = IncrementalUpdater(db_path)
    
    def sync_with_source(self, source_data: List[Dict[str, Any]], 
                        sync_mode: str = "incremental") -> Dict[str, Any]:
        """与数据源同步"""
        logger.info(f"开始数据同步，模式: {sync_mode}")
        
        if sync_mode == "incremental":
            return self._incremental_sync(source_data)
        elif sync_mode == "full":
            return self._full_sync(source_data)
        else:
            raise ValueError(f"不支持的同步模式: {sync_mode}")
    
    def _incremental_sync(self, source_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """增量同步"""
        # 获取最新期号
        latest_issue = self.updater.get_latest_issue()
        
        if latest_issue:
            # 只处理比最新期号更新的数据
            filtered_data = [
                record for record in source_data
                if record.get('issue', '') > latest_issue
            ]
            logger.info(f"增量同步: 最新期号 {latest_issue}, 待处理 {len(filtered_data)} 条新记录")
        else:
            # 数据库为空，处理所有数据
            filtered_data = source_data
            logger.info(f"首次同步: 处理所有 {len(filtered_data)} 条记录")
        
        return self.updater.process_incremental_update(filtered_data)
    
    def _full_sync(self, source_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """全量同步"""
        logger.info(f"全量同步: 处理所有 {len(source_data)} 条记录")
        return self.updater.process_incremental_update(source_data, force_update=True)
    
    def verify_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            with self.updater.get_connection() as conn:
                # 检查数据统计
                cursor = conn.execute("SELECT COUNT(*) as total FROM lottery_data")
                total_records = cursor.fetchone()['total']
                
                # 检查日期范围
                cursor = conn.execute(
                    "SELECT MIN(draw_date) as min_date, MAX(draw_date) as max_date FROM lottery_data"
                )
                date_range = cursor.fetchone()
                
                # 检查期号连续性
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data ORDER BY issue"
                )
                issues = [row['issue'] for row in cursor.fetchall()]
                
                # 分析期号间隔
                gaps = []
                if len(issues) > 1:
                    for i in range(1, len(issues)):
                        prev_num = int(issues[i-1])
                        curr_num = int(issues[i])
                        if curr_num - prev_num > 1:
                            gaps.append((issues[i-1], issues[i]))
                
                integrity_report = {
                    'total_records': total_records,
                    'date_range': {
                        'min_date': date_range['min_date'],
                        'max_date': date_range['max_date']
                    },
                    'issue_gaps': gaps,
                    'gap_count': len(gaps),
                    'integrity_score': 1.0 - (len(gaps) / max(total_records, 1))
                }
                
                logger.info(f"数据完整性检查完成:")
                logger.info(f"  总记录数: {total_records}")
                logger.info(f"  日期范围: {date_range['min_date']} ~ {date_range['max_date']}")
                logger.info(f"  期号间隔: {len(gaps)} 个")
                logger.info(f"  完整性评分: {integrity_report['integrity_score']:.2%}")
                
                return integrity_report
                
        except Exception as e:
            logger.error(f"数据完整性检查失败: {e}")
            return {'error': str(e)}


if __name__ == "__main__":
    # 测试增量更新器
    print("=== 福彩3D增量更新器测试 ===")
    
    # 创建测试数据
    test_data = [
        {
            'issue': '2024004',
            'draw_date': '2024-01-04',
            'hundreds': 7,
            'tens': 8,
            'units': 9,
            'sum_value': 24,
            'span': 2,
            'number_type': '组六'
        },
        {
            'issue': '2024005',
            'draw_date': '2024-01-05',
            'hundreds': 0,
            'tens': 1,
            'units': 2,
            'sum_value': 3,
            'span': 2,
            'number_type': '组六'
        }
    ]
    
    # 测试增量更新
    updater = IncrementalUpdater()
    
    # 获取最新期号
    latest_issue = updater.get_latest_issue()
    print(f"数据库最新期号: {latest_issue}")
    
    # 处理增量更新
    result = updater.process_incremental_update(test_data)
    
    print(f"更新结果:")
    print(f"  新增记录: {result['new_records']}")
    print(f"  更新记录: {result['updated_records']}")
    print(f"  跳过记录: {result['skipped_records']}")
    print(f"  处理时间: {result['processing_time']:.2f} 秒")
    
    # 测试数据同步器
    print(f"\n=== 数据同步器测试 ===")
    synchronizer = DataSynchronizer()
    
    # 验证数据完整性
    integrity = synchronizer.verify_data_integrity()
    print(f"数据完整性:")
    print(f"  总记录数: {integrity.get('total_records', 0)}")
    print(f"  完整性评分: {integrity.get('integrity_score', 0):.2%}")
    
    print(f"\n🎉 增量更新器测试完成！")


def update_from_17500_desc() -> bool:
    """从17500.cn的desc数据源更新数据"""
    try:
        # 导入完整数据采集器
        from .complete_collector import IntegratedCompleteCollector

        logger.info("🔄 开始从17500 desc数据源更新...")

        # 创建采集器
        collector = IntegratedCompleteCollector()

        # 执行完整采集
        success = collector.run_complete_collection()

        if success:
            logger.info("✅ 17500 desc数据源更新成功")
        else:
            logger.error("❌ 17500 desc数据源更新失败")

        return success

    except Exception as e:
        logger.error(f"❌ 17500 desc数据源更新异常: {e}")
        return False


def schedule_daily_update():
    """安排每日自动更新"""
    try:
        import schedule

        # 每天晚上21:30执行更新
        schedule.every().day.at("21:30").do(update_from_17500_desc)

        logger.info("✅ 已安排每日21:30自动更新")

        # 运行调度器
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    except ImportError:
        logger.warning("⚠️ schedule库未安装，无法设置自动更新")
        logger.info("💡 可以手动运行 update_from_17500_desc() 进行更新")
    except Exception as e:
        logger.error(f"❌ 调度器运行失败: {e}")


if __name__ == "__main__":
    # 测试新的更新功能
    print("🚀 测试17500 desc数据源更新...")
    success = update_from_17500_desc()

    if success:
        print("✅ 更新测试成功")
    else:
        print("❌ 更新测试失败")
