#!/usr/bin/env python3
"""
测试真实数据采集
验证是否能够采集到真实的福彩3D开奖数据
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

def test_real_data_collection():
    """测试真实数据采集"""
    print("=== 真实数据采集测试 ===")

    try:
        from data.collector import LotteryDataCollector
        from database.models import DatabaseManager

        print("✅ 模块导入成功")

        # 创建采集器
        collector = LotteryDataCollector()
        print("✅ 采集器创建成功")

        # 尝试采集真实数据
        print("开始采集真实数据...")
        data = collector.collect_data(limit=10)

        print(f"采集结果: {len(data)} 条数据")

        if data:
            print("\n✅ 成功采集到真实数据!")
            print("数据样例:")
            for i, record in enumerate(data[:5]):
                issue = record.get('issue', 'N/A')
                hundreds = record.get('hundreds', 'N/A')
                tens = record.get('tens', 'N/A')
                units = record.get('units', 'N/A')
                draw_date = record.get('draw_date', 'N/A')

                print(f"  {i+1}. 期号: {issue}, 开奖号码: {hundreds}{tens}{units}, 日期: {draw_date}")

            # 验证数据真实性 - 更严格的验证
            real_data_count = 0
            virtual_data_indicators = ['2024001', '2024002', '2024003', '123', '555', '112']

            for record in data:
                issue = str(record.get('issue', ''))
                numbers = f"{record.get('hundreds', '')}{record.get('tens', '')}{record.get('units', '')}"

                # 检查是否为虚拟数据
                is_virtual = any(indicator in issue or indicator in numbers for indicator in virtual_data_indicators)

                # 检查真实期号格式（7位数字，且不是连续编号）
                if len(issue) == 7 and issue.isdigit() and not is_virtual:
                    # 进一步验证：真实期号不应该是简单的连续编号
                    if not (issue.startswith('2024') and issue.endswith(('001', '002', '003'))):
                        real_data_count += 1

            print(f"\n数据真实性验证:")
            print(f"  总数据: {len(data)} 条")
            print(f"  真实格式数据: {real_data_count} 条")
            print(f"  真实性比例: {real_data_count/len(data)*100:.1f}%")

            if real_data_count > 0:
                print("✅ 确认采集到真实福彩3D数据")
                return True
            else:
                print("❌ 采集的数据不是真实福彩3D格式或仍包含虚拟数据")
                return False
        else:
            print("❌ 未能采集到任何数据")
            return False

    except Exception as e:
        print(f"❌ 采集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_sources():
    """测试数据源可用性"""
    print("\n=== 数据源测试 ===")
    
    try:
        from config.data_sources import DataSourceValidator
        
        validator = DataSourceValidator()
        results = validator.validate_all_sources()
        
        print(f"数据源验证结果:")
        print(f"  总数据源: {results['total_sources']}")
        print(f"  有效数据源: {results['valid_sources']}")
        print(f"  最佳数据源: {results['best_source']}")
        
        if results['valid_sources'] > 0:
            print("✅ 有可用的数据源")
            return True
        else:
            print("❌ 没有可用的数据源")
            return False
            
    except Exception as e:
        print(f"❌ 数据源测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始P1模块真实数据验证...")
    
    # 测试数据源
    source_ok = test_data_sources()
    
    # 测试数据采集
    data_ok = test_real_data_collection()
    
    print("\n=== 验证总结 ===")
    print(f"数据源状态: {'✅ 正常' if source_ok else '❌ 异常'}")
    print(f"数据采集状态: {'✅ 正常' if data_ok else '❌ 异常'}")
    
    if source_ok and data_ok:
        print("🎉 P1模块真实数据验证通过!")
        return True
    else:
        print("⚠️ P1模块存在真实数据问题，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
