"""
数据采集器模块
基于3dyuce项目成功经验，实现稳定的数据采集功能
"""

import requests
import time
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import re
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LotteryDataCollector:
    """福彩3D数据采集器"""
    
    def __init__(self, config=None):
        self.config = config or self._get_default_config()
        self.session = requests.Session()
        self.collected_data = []
        self.collection_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_records': 0
        }
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'primary_sources': [
                "https://www.17500.cn/chart/3d-tjb.html",
                "https://data.17500.cn/3d_asc.txt"
            ],
            'backup_sources': [
                "https://www.cjcp.com.cn/kaijiang/3dmingxi_0.html"
            ],
            'request_timeout': 30,
            'max_retries': 3,
            'base_delay': 2,
            'max_delay': 10,
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        }
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.config['user_agents']),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
    
    def safe_request(self, url: str, retries: int = None) -> Optional[requests.Response]:
        """安全的HTTP请求，包含重试机制"""
        if retries is None:
            retries = self.config['max_retries']
        
        for attempt in range(retries + 1):
            try:
                self.collection_stats['total_requests'] += 1
                
                headers = self.get_random_headers()
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=self.config['request_timeout']
                )
                
                if response.status_code == 200:
                    self.collection_stats['successful_requests'] += 1
                    logger.info(f"成功请求: {url} (尝试 {attempt + 1}/{retries + 1})")
                    return response
                else:
                    logger.warning(f"HTTP错误 {response.status_code}: {url}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时: {url} (尝试 {attempt + 1}/{retries + 1})")
            except requests.exceptions.ConnectionError:
                logger.warning(f"连接错误: {url} (尝试 {attempt + 1}/{retries + 1})")
            except Exception as e:
                logger.error(f"请求异常: {url} - {str(e)}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < retries:
                delay = min(
                    self.config['base_delay'] * (2 ** attempt) + random.uniform(0, 1),
                    self.config['max_delay']
                )
                logger.info(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
        
        self.collection_stats['failed_requests'] += 1
        logger.error(f"所有重试失败: {url}")
        return None
    
    def collect_from_html_source(self, url: str) -> List[Dict]:
        """从HTML数据源采集数据"""
        logger.info(f"开始从HTML源采集数据: {url}")
        
        response = self.safe_request(url)
        if not response:
            return []
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            data_list = []
            
            # 查找数据表格
            tables = soup.find_all('table', class_='chart_table')
            if not tables:
                # 尝试其他可能的表格选择器
                tables = soup.find_all('table')
                tables = [t for t in tables if '期号' in t.get_text() or '开奖' in t.get_text()]
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows[1:]:  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 5:  # 至少包含期号、日期、开奖号码
                        try:
                            data = self._parse_html_row(cells)
                            if data:
                                data_list.append(data)
                        except Exception as e:
                            logger.warning(f"解析行数据失败: {str(e)}")
                            continue
            
            logger.info(f"从HTML源采集到 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            logger.error(f"HTML数据解析失败: {str(e)}")
            return []
    
    def _parse_html_row(self, cells) -> Optional[Dict]:
        """解析HTML表格行数据"""
        try:
            # 提取文本内容
            cell_texts = [cell.get_text().strip() for cell in cells]
            
            # 查找期号（通常是第一列）
            issue = cell_texts[0]
            if not re.match(r'\d{7}', issue):  # 期号格式验证
                return None
            
            # 查找日期
            date_text = ""
            for text in cell_texts[1:3]:
                if re.match(r'\d{4}-\d{2}-\d{2}', text):
                    date_text = text
                    break
            
            # 查找开奖号码
            numbers = []
            for text in cell_texts:
                # 查找三位数字
                number_match = re.search(r'(\d)\s*(\d)\s*(\d)', text)
                if number_match:
                    numbers = [int(number_match.group(1)), 
                              int(number_match.group(2)), 
                              int(number_match.group(3))]
                    break
            
            if not numbers or len(numbers) != 3:
                return None
            
            # 构建数据记录
            data = {
                'issue': issue,
                'draw_date': date_text,
                'hundreds': numbers[0],
                'tens': numbers[1],
                'units': numbers[2],
                'sum_value': sum(numbers),
                'span': max(numbers) - min(numbers),
                'number_type': self._determine_number_type(numbers)
            }
            
            # 尝试提取试机号码（如果存在）
            trial_numbers = self._extract_trial_numbers(cell_texts)
            if trial_numbers:
                data.update({
                    'trial_hundreds': trial_numbers[0],
                    'trial_tens': trial_numbers[1],
                    'trial_units': trial_numbers[2]
                })
            
            return data
            
        except Exception as e:
            logger.warning(f"解析HTML行失败: {str(e)}")
            return None
    
    def collect_from_text_source(self, url: str) -> List[Dict]:
        """从文本数据源采集数据"""
        logger.info(f"开始从文本源采集数据: {url}")
        
        response = self.safe_request(url)
        if not response:
            return []
        
        try:
            lines = response.text.strip().split('\n')
            data_list = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = self._parse_text_line(line)
                    if data:
                        data_list.append(data)
                except Exception as e:
                    logger.warning(f"解析文本行失败: {line} - {str(e)}")
                    continue
            
            logger.info(f"从文本源采集到 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            logger.error(f"文本数据解析失败: {str(e)}")
            return []
    
    def _parse_text_line(self, line: str) -> Optional[Dict]:
        """解析文本行数据"""
        try:
            # 常见的文本格式：期号,日期,开奖号码,试机号码,销售额等
            parts = line.split(',')
            if len(parts) < 3:
                return None
            
            issue = parts[0].strip()
            if not re.match(r'\d{7}', issue):
                return None
            
            date_text = parts[1].strip()
            
            # 解析开奖号码
            number_text = parts[2].strip()
            number_match = re.search(r'(\d)(\d)(\d)', number_text)
            if not number_match:
                return None
            
            numbers = [int(number_match.group(1)), 
                      int(number_match.group(2)), 
                      int(number_match.group(3))]
            
            data = {
                'issue': issue,
                'draw_date': date_text,
                'hundreds': numbers[0],
                'tens': numbers[1],
                'units': numbers[2],
                'sum_value': sum(numbers),
                'span': max(numbers) - min(numbers),
                'number_type': self._determine_number_type(numbers)
            }
            
            # 尝试解析试机号码
            if len(parts) > 3:
                trial_text = parts[3].strip()
                trial_match = re.search(r'(\d)(\d)(\d)', trial_text)
                if trial_match:
                    data.update({
                        'trial_hundreds': int(trial_match.group(1)),
                        'trial_tens': int(trial_match.group(2)),
                        'trial_units': int(trial_match.group(3))
                    })
            
            # 尝试解析销售额
            if len(parts) > 4:
                sales_text = parts[4].strip()
                sales_match = re.search(r'([\d,]+)', sales_text)
                if sales_match:
                    sales_amount = float(sales_match.group(1).replace(',', ''))
                    data['sales_amount'] = sales_amount
            
            return data
            
        except Exception as e:
            logger.warning(f"解析文本行失败: {str(e)}")
            return None
    
    def _extract_trial_numbers(self, cell_texts: List[str]) -> Optional[List[int]]:
        """提取试机号码"""
        for text in cell_texts:
            # 查找试机号码模式
            if '试机' in text or 'trial' in text.lower():
                number_match = re.search(r'(\d)(\d)(\d)', text)
                if number_match:
                    return [int(number_match.group(1)), 
                           int(number_match.group(2)), 
                           int(number_match.group(3))]
        return None
    
    def _determine_number_type(self, numbers: List[int]) -> str:
        """判断号码类型"""
        unique_count = len(set(numbers))
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def collect_data(self, source_url: str = None, limit: int = None) -> List[Dict]:
        """主要的数据采集方法"""
        start_time = time.time()
        logger.info("开始数据采集...")
        
        sources = [source_url] if source_url else (
            self.config['primary_sources'] + self.config['backup_sources']
        )
        
        all_data = []
        
        for url in sources:
            try:
                logger.info(f"尝试数据源: {url}")
                
                if url.endswith('.txt'):
                    data = self.collect_from_text_source(url)
                else:
                    data = self.collect_from_html_source(url)
                
                if data:
                    all_data.extend(data)
                    logger.info(f"从 {url} 采集到 {len(data)} 条数据")
                    
                    # 如果已经获得足够数据，可以停止
                    if limit and len(all_data) >= limit:
                        all_data = all_data[:limit]
                        break
                else:
                    logger.warning(f"从 {url} 未采集到数据")
                
                # 请求间隔
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                logger.error(f"采集数据源 {url} 失败: {str(e)}")
                continue
        
        # 去重处理
        unique_data = self._deduplicate_data(all_data)
        
        collection_time = time.time() - start_time
        self.collection_stats['total_records'] = len(unique_data)
        
        logger.info(f"数据采集完成:")
        logger.info(f"  采集时间: {collection_time:.2f} 秒")
        logger.info(f"  总请求数: {self.collection_stats['total_requests']}")
        logger.info(f"  成功请求: {self.collection_stats['successful_requests']}")
        logger.info(f"  失败请求: {self.collection_stats['failed_requests']}")
        logger.info(f"  采集记录: {len(unique_data)} 条")
        
        return unique_data
    
    def _deduplicate_data(self, data_list: List[Dict]) -> List[Dict]:
        """数据去重"""
        seen_issues = set()
        unique_data = []
        
        for data in data_list:
            issue = data.get('issue')
            if issue and issue not in seen_issues:
                seen_issues.add(issue)
                unique_data.append(data)
        
        logger.info(f"去重前: {len(data_list)} 条，去重后: {len(unique_data)} 条")
        return unique_data
    
    def get_collection_stats(self) -> Dict:
        """获取采集统计信息"""
        return self.collection_stats.copy()


if __name__ == "__main__":
    # 测试数据采集器
    print("=== 福彩3D数据采集器测试 ===")

    collector = LotteryDataCollector()

    # 测试采集
    data = collector.collect_data(limit=5)

    print(f"\n采集结果:")
    for i, record in enumerate(data[:3], 1):
        print(f"{i}. 期号: {record.get('issue')}, "
              f"日期: {record.get('draw_date')}, "
              f"号码: {record.get('hundreds')}{record.get('tens')}{record.get('units')}")

    # 显示统计信息
    stats = collector.get_collection_stats()
    print(f"\n统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
