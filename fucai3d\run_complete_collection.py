#!/usr/bin/env python3
"""
运行完整数据采集的简化脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """主函数"""
    try:
        from complete_data_collector import CompleteDataCollector
        
        print("🚀 开始福彩3D完整数据采集...")
        print("📊 目标：从2002001到最新一期2025204的所有数据")
        print("🌐 数据源：https://data.17500.cn/3d_desc.txt")
        print("=" * 60)
        
        # 创建采集器
        collector = CompleteDataCollector()
        
        # 执行完整采集
        success = collector.run_complete_collection()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 福彩3D完整数据库构建成功！")
            print("💾 数据库文件：data/lottery.db")
            print("📈 包含从2002001到最新一期的所有历史数据")
            print("🔄 可定期运行此脚本更新最新数据")
            print("✅ 严格遵守'禁止虚拟数据'原则，全部为真实数据")
        else:
            print("\n" + "=" * 60)
            print("❌ 数据采集失败")
            print("🔧 请检查网络连接和数据源可用性")
        
        return success
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保complete_data_collector.py文件存在")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 下一步建议：")
        print("1. 验证数据库内容：python -c \"import sqlite3; conn=sqlite3.connect('data/lottery.db'); print('总记录数:', conn.execute('SELECT COUNT(*) FROM lottery_data').fetchone()[0]); conn.close()\"")
        print("2. 查看数据样例：python -c \"import sqlite3; conn=sqlite3.connect('data/lottery.db'); rows=conn.execute('SELECT * FROM lottery_data ORDER BY issue DESC LIMIT 5').fetchall(); [print(f'期号:{r[1]} 日期:{r[2]} 号码:{r[3]}{r[4]}{r[5]}') for r in rows]; conn.close()\"")
        print("3. 设置定时任务定期更新数据")
    
    sys.exit(0 if success else 1)
