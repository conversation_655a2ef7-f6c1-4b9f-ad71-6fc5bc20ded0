#!/usr/bin/env python3
"""
简化的真实数据采集测试
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

def test_simple_collection():
    """简化的数据采集测试"""
    print("=== 简化真实数据采集测试 ===")
    
    try:
        # 直接测试HTTP请求和HTML解析
        import requests
        from bs4 import BeautifulSoup
        import re
        
        # 测试可用的数据源
        url = 'https://www.17500.cn/chart/3d-tjb.html'
        print(f"测试数据源: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找表格
            tables = soup.find_all('table')
            print(f"找到 {len(tables)} 个表格")
            
            real_data_found = False
            
            for i, table in enumerate(tables):
                text = table.get_text()
                if '期号' in text or '开奖' in text or '3D' in text:
                    print(f"\n表格 {i+1} 包含福彩3D信息")
                    rows = table.find_all('tr')
                    print(f"行数: {len(rows)}")
                    
                    # 分析前10行数据
                    for j, row in enumerate(rows[:10]):
                        cells = row.find_all(['td', 'th'])
                        cell_texts = [cell.get_text().strip() for cell in cells if cell.get_text().strip()]
                        
                        if len(cell_texts) >= 3:
                            print(f"行 {j+1}: {cell_texts[:6]}")  # 显示前6列
                            
                            # 检查是否包含真实期号
                            for text in cell_texts[:3]:
                                if re.match(r'^\d{7}$', text) and not text.endswith(('001', '002', '003')):
                                    print(f"  ✅ 发现真实期号: {text}")
                                    real_data_found = True
                                    
                                    # 查找对应的开奖号码
                                    for num_text in cell_texts:
                                        if re.match(r'^\d{3}$', num_text):
                                            print(f"  ✅ 发现开奖号码: {num_text}")
                                            break
                    
                    if real_data_found:
                        break
            
            if real_data_found:
                print("\n🎉 成功发现真实福彩3D数据！")
                return True
            else:
                print("\n❌ 未发现真实福彩3D数据")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_source_availability():
    """测试数据源可用性"""
    print("\n=== 数据源可用性测试 ===")
    
    sources = [
        'https://www.17500.cn/chart/3d-tjb.html',
        'https://www.cjcp.com.cn/kaijiang/3dmingxi_0.html'
    ]
    
    available_sources = []
    
    for url in sources:
        try:
            import requests
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {url} - 可用")
                available_sources.append(url)
            else:
                print(f"❌ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
    
    print(f"\n可用数据源: {len(available_sources)}/{len(sources)}")
    return available_sources

def main():
    """主函数"""
    print("开始P1模块真实数据采集验证...")
    
    # 测试数据源可用性
    available_sources = test_data_source_availability()
    
    if not available_sources:
        print("❌ 没有可用的数据源")
        return False
    
    # 测试数据采集
    success = test_simple_collection()
    
    print("\n=== 验证总结 ===")
    print(f"可用数据源: {len(available_sources)}")
    print(f"真实数据采集: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 P1模块真实数据验证通过!")
        print("✅ 确认能够采集到真实的福彩3D开奖数据")
        print("✅ 数据格式符合要求（7位期号，3位开奖号码）")
        print("✅ 无虚拟测试数据")
        return True
    else:
        print("⚠️ P1模块需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
