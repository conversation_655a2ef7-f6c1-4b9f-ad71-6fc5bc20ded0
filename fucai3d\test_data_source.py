#!/usr/bin/env python3
"""
测试数据源格式
"""

import requests
import re

def test_data_source():
    """测试数据源"""
    url = "https://data.17500.cn/3d_desc.txt"
    
    print(f"🔄 测试数据源: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            # 尝试不同编码
            try:
                content = response.content.decode('utf-8')
            except:
                try:
                    content = response.content.decode('gbk')
                except:
                    content = response.content.decode('utf-8', errors='ignore')
            
            print(f"内容长度: {len(content)} 字符")
            
            # 分析前几行
            lines = content.strip().split('\n')[:20]
            print(f"总行数: {len(content.strip().split('\n'))}")
            print(f"前20行数据:")
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    print(f"  {i:2d}: {line}")
                    
                    # 检查数据格式
                    if re.search(r'\d{7}', line):
                        print(f"      ✅ 包含期号")
                    if re.search(r'\d{4}-\d{2}-\d{2}', line):
                        print(f"      ✅ 包含日期")
                    if re.search(r'\d{3}', line):
                        print(f"      ✅ 包含号码")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_data_source()
