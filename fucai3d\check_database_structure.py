#!/usr/bin/env python3
"""
检查现有数据库结构
"""

import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    db_path = "data/lottery.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 数据库结构分析")
        print("=" * 50)
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 数据库中的表: {len(tables)} 个")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查lottery_data表结构
        if ('lottery_data',) in tables:
            print(f"\n📋 lottery_data表结构:")
            cursor.execute("PRAGMA table_info(lottery_data)")
            columns = cursor.fetchall()
            
            for col in columns:
                print(f"  {col[1]:20} {col[2]:15} {'NOT NULL' if col[3] else 'NULL':8} {'PK' if col[5] else '':3}")
            
            # 获取数据统计
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            count = cursor.fetchone()[0]
            print(f"\n📈 数据统计:")
            print(f"  总记录数: {count:,} 条")
            
            if count > 0:
                # 获取期号范围
                cursor.execute("SELECT MIN(issue), MAX(issue) FROM lottery_data")
                min_issue, max_issue = cursor.fetchone()
                print(f"  期号范围: {min_issue} - {max_issue}")
                
                # 获取日期范围
                cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_data")
                min_date, max_date = cursor.fetchone()
                print(f"  日期范围: {min_date} - {max_date}")
                
                # 检查最新几条数据
                print(f"\n📝 最新5条数据:")
                cursor.execute("SELECT * FROM lottery_data ORDER BY issue DESC LIMIT 5")
                recent_data = cursor.fetchall()
                
                for row in recent_data:
                    print(f"  期号: {row[1]}, 日期: {row[2]}, 号码: {row[3]}{row[4]}{row[5]}")
        
        # 检查索引
        print(f"\n🔗 索引信息:")
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='lottery_data'")
        indexes = cursor.fetchall()
        
        for idx in indexes:
            print(f"  - {idx[0]}: {idx[1] if idx[1] else 'AUTO'}")
        
        conn.close()
        
        print(f"\n✅ 数据库结构分析完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

if __name__ == "__main__":
    check_database_structure()
