#!/usr/bin/env python3
"""
简单的数据库测试脚本
"""

import os
import sys
import sqlite3
from pathlib import Path

# 创建数据目录
os.makedirs("data", exist_ok=True)

# 数据库路径
db_path = "data/lottery.db"

# 创建数据库连接
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 创建主数据表
cursor.execute("""
CREATE TABLE IF NOT EXISTS lottery_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT UNIQUE NOT NULL,
    draw_date TEXT NOT NULL,
    hundreds INTEGER NOT NULL CHECK (hundreds >= 0 AND hundreds <= 9),
    tens INTEGER NOT NULL CHECK (tens >= 0 AND tens <= 9),
    units INTEGER NOT NULL CHECK (units >= 0 AND units <= 9),
    trial_hundreds INTEGER CHECK (trial_hundreds IS NULL OR (trial_hundreds >= 0 AND trial_hundreds <= 9)),
    trial_tens INTEGER CHECK (trial_tens IS NULL OR (trial_tens >= 0 AND trial_tens <= 9)),
    trial_units INTEGER CHECK (trial_units IS NULL OR (trial_units >= 0 AND trial_units <= 9)),
    machine_number TEXT,
    sales_amount REAL CHECK (sales_amount IS NULL OR sales_amount >= 0),
    prize_info TEXT,
    sum_value INTEGER CHECK (sum_value >= 0 AND sum_value <= 27),
    span INTEGER CHECK (span >= 0 AND span <= 9),
    number_type TEXT CHECK (number_type IN ('豹子', '对子', '组六')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

# 创建采集日志表
cursor.execute("""
CREATE TABLE IF NOT EXISTS collection_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    collection_time TEXT NOT NULL,
    source_url TEXT NOT NULL,
    records_collected INTEGER DEFAULT 0,
    success BOOLEAN DEFAULT 1,
    error_message TEXT,
    response_time REAL DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

# 创建数据验证表
cursor.execute("""
CREATE TABLE IF NOT EXISTS data_validation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    validation_time TEXT NOT NULL,
    issue TEXT NOT NULL,
    validation_type TEXT NOT NULL,
    is_valid BOOLEAN DEFAULT 1,
    error_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

# 创建索引
indexes = [
    "CREATE INDEX IF NOT EXISTS idx_lottery_data_issue ON lottery_data (issue)",
    "CREATE INDEX IF NOT EXISTS idx_lottery_data_date ON lottery_data (draw_date)",
    "CREATE INDEX IF NOT EXISTS idx_lottery_data_numbers ON lottery_data (hundreds, tens, units)"
]

for index_sql in indexes:
    cursor.execute(index_sql)

# 插入测试数据
test_data = [
    ("2024001", "2024-01-01", 1, 2, 3, 4, 5, 6, "M001", 1000000.0, "一等奖1注", 6, 2, "组六"),
    ("2024002", "2024-01-02", 5, 5, 5, 1, 2, 3, "M002", 1200000.0, "一等奖2注", 15, 0, "豹子"),
    ("2024003", "2024-01-03", 1, 1, 2, 7, 8, 9, "M001", 980000.0, "一等奖1注", 4, 1, "对子")
]

for data in test_data:
    cursor.execute("""
    INSERT OR REPLACE INTO lottery_data 
    (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
     machine_number, sales_amount, prize_info, sum_value, span, number_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, data)

# 提交更改
conn.commit()

# 验证数据
cursor.execute("SELECT COUNT(*) FROM lottery_data")
count = cursor.fetchone()[0]

cursor.execute("SELECT * FROM lottery_data ORDER BY issue")
records = cursor.fetchall()

print("=== 数据库测试结果 ===")
print(f"✅ 数据库创建成功: {db_path}")
print(f"✅ 数据表创建成功")
print(f"✅ 索引创建成功")
print(f"✅ 测试数据插入成功")
print(f"📊 数据记录数: {count}")

print(f"\n📋 数据记录:")
for record in records:
    print(f"  期号: {record[1]}, 开奖号码: {record[3]}{record[4]}{record[5]}, 试机号: {record[6]}{record[7]}{record[8]}")

# 关闭连接
conn.close()

print(f"\n🎉 数据库初始化完成！")
