#!/usr/bin/env python3
"""
完整的福彩3D数据采集器
从 https://data.17500.cn/3d_desc.txt 采集从2002001到最新一期的所有数据
"""

import sys
import os
import sqlite3
import requests
import re
from datetime import datetime
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

class CompleteDataCollector:
    """完整的福彩3D数据采集器"""
    
    def __init__(self):
        self.data_source_url = "https://data.17500.cn/3d_desc.txt"
        self.db_path = "data/lottery.db"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        
    def fetch_raw_data(self):
        """获取原始数据"""
        print(f"🔄 开始从数据源获取数据: {self.data_source_url}")
        
        try:
            response = requests.get(self.data_source_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # 尝试不同的编码
            try:
                content = response.content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    content = response.content.decode('gbk')
                except UnicodeDecodeError:
                    content = response.content.decode('utf-8', errors='ignore')
            
            print(f"✅ 数据获取成功，内容长度: {len(content)} 字符")
            return content
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None
    
    def parse_data(self, raw_content):
        """解析数据"""
        if not raw_content:
            return []
        
        print("🔄 开始解析数据...")
        
        lines = raw_content.strip().split('\n')
        parsed_data = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                # 尝试多种数据格式解析
                data = self._parse_line(line)
                if data:
                    parsed_data.append(data)
                    
            except Exception as e:
                print(f"⚠️ 第{line_num}行解析失败: {line[:50]}... 错误: {e}")
                continue
        
        print(f"✅ 数据解析完成，成功解析 {len(parsed_data)} 条记录")
        return parsed_data
    
    def _parse_line(self, line):
        """解析单行数据"""
        # 移除多余空格，标准化为单个空格分隔
        line = re.sub(r'\s+', ' ', line.strip())

        # 基于实际数据源格式：期号 日期 开奖号码 试机号码
        # 示例：2025204 2025-07-23 497 728
        pattern = r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})\s+(\d{3})'

        match = re.match(pattern, line)
        if not match:
            # 如果标准格式不匹配，尝试其他可能的格式
            fallback_patterns = [
                # 只有期号、日期、开奖号码
                r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})',
                # 逗号分隔格式
                r'(\d{7}),(\d{4}-\d{2}-\d{2}),(\d{3}),(\d{3})',
                r'(\d{7}),(\d{4}-\d{2}-\d{2}),(\d{3})',
            ]

            for fallback_pattern in fallback_patterns:
                match = re.match(fallback_pattern, line)
                if match:
                    break

            if not match:
                return None

        # 解析匹配的数据
        groups = match.groups()

        issue = groups[0]
        date = groups[1]
        numbers = groups[2]

        # 验证数据有效性
        if not self._validate_data(issue, date, numbers):
            return None

        # 解析开奖号码
        hundreds = int(numbers[0])
        tens = int(numbers[1])
        units = int(numbers[2])

        # 试机号码（如果存在）
        trial_numbers = groups[3] if len(groups) > 3 and len(groups[3]) == 3 and groups[3].isdigit() else None
        trial_hundreds = int(trial_numbers[0]) if trial_numbers else None
        trial_tens = int(trial_numbers[1]) if trial_numbers else None
        trial_units = int(trial_numbers[2]) if trial_numbers else None

        # 计算和值、跨度等
        sum_value = hundreds + tens + units
        span = max(hundreds, tens, units) - min(hundreds, tens, units)

        # 判断号码类型
        number_type = self._get_number_type(hundreds, tens, units)

        return {
            'issue': issue,
            'draw_date': date,
            'hundreds': hundreds,
            'tens': tens,
            'units': units,
            'trial_hundreds': trial_hundreds,
            'trial_tens': trial_tens,
            'trial_units': trial_units,
            'sum_value': sum_value,
            'span': span,
            'number_type': number_type,
            'machine_number': None,
            'sales_amount': None,
            'prize_info': None
        }
    
    def _validate_data(self, issue, date, numbers):
        """验证数据有效性"""
        # 验证期号格式
        if not re.match(r'^\d{7}$', issue):
            return False
        
        # 验证期号范围
        issue_num = int(issue)
        if issue_num < 2002001 or issue_num > 2030365:
            return False
        
        # 验证日期格式
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
            return False
        
        # 验证号码格式
        if not re.match(r'^\d{3}$', numbers):
            return False
        
        return True
    
    def _get_number_type(self, hundreds, tens, units):
        """判断号码类型"""
        numbers = [hundreds, tens, units]
        unique_count = len(set(numbers))
        
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def save_to_database(self, data_list):
        """保存到数据库"""
        if not data_list:
            print("❌ 没有数据需要保存")
            return False
        
        print(f"🔄 开始保存 {len(data_list)} 条数据到数据库...")
        
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT UNIQUE NOT NULL,
                    draw_date TEXT NOT NULL,
                    hundreds INTEGER NOT NULL,
                    tens INTEGER NOT NULL,
                    units INTEGER NOT NULL,
                    trial_hundreds INTEGER,
                    trial_tens INTEGER,
                    trial_units INTEGER,
                    machine_number TEXT,
                    sales_amount REAL,
                    prize_info TEXT,
                    sum_value INTEGER,
                    span INTEGER,
                    number_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 批量插入数据
            insert_count = 0
            update_count = 0
            
            for data in data_list:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO lottery_data 
                        (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
                         machine_number, sales_amount, prize_info, sum_value, span, number_type)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data['issue'], data['draw_date'], data['hundreds'], data['tens'], data['units'],
                        data['trial_hundreds'], data['trial_tens'], data['trial_units'],
                        data['machine_number'], data['sales_amount'], data['prize_info'],
                        data['sum_value'], data['span'], data['number_type']
                    ))
                    
                    if cursor.rowcount > 0:
                        insert_count += 1
                    
                except sqlite3.IntegrityError:
                    update_count += 1
                    continue
            
            conn.commit()
            conn.close()
            
            print(f"✅ 数据保存完成:")
            print(f"  新增记录: {insert_count} 条")
            print(f"  更新记录: {update_count} 条")
            print(f"  总计处理: {len(data_list)} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库保存失败: {e}")
            return False
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总记录数
            cursor.execute('SELECT COUNT(*) FROM lottery_data')
            total_count = cursor.fetchone()[0]
            
            # 最早和最新期号
            cursor.execute('SELECT MIN(issue), MAX(issue) FROM lottery_data')
            min_issue, max_issue = cursor.fetchone()
            
            # 最早和最新日期
            cursor.execute('SELECT MIN(draw_date), MAX(draw_date) FROM lottery_data')
            min_date, max_date = cursor.fetchone()
            
            conn.close()
            
            return {
                'total_count': total_count,
                'min_issue': min_issue,
                'max_issue': max_issue,
                'min_date': min_date,
                'max_date': max_date
            }
            
        except Exception as e:
            print(f"❌ 获取数据库统计失败: {e}")
            return None
    
    def run_complete_collection(self):
        """执行完整的数据采集"""
        print("🚀 开始完整的福彩3D数据采集...")
        print(f"📊 目标: 从2002001到最新一期的所有数据")
        print(f"🌐 数据源: {self.data_source_url}")
        
        # 1. 获取原始数据
        raw_data = self.fetch_raw_data()
        if not raw_data:
            return False
        
        # 2. 解析数据
        parsed_data = self.parse_data(raw_data)
        if not parsed_data:
            print("❌ 没有解析到有效数据")
            return False
        
        # 3. 保存到数据库
        success = self.save_to_database(parsed_data)
        if not success:
            return False
        
        # 4. 显示统计信息
        stats = self.get_database_stats()
        if stats:
            print(f"\n📈 数据库统计信息:")
            print(f"  总记录数: {stats['total_count']} 条")
            print(f"  期号范围: {stats['min_issue']} - {stats['max_issue']}")
            print(f"  日期范围: {stats['min_date']} - {stats['max_date']}")
        
        print(f"\n🎉 完整数据采集成功完成!")
        return True

def main():
    """主函数"""
    collector = CompleteDataCollector()
    success = collector.run_complete_collection()
    
    if success:
        print("\n✅ 福彩3D完整数据库构建成功!")
        print("💡 数据库文件位置: data/lottery.db")
        print("🔄 可以定期运行此脚本来更新最新数据")
    else:
        print("\n❌ 数据采集失败，请检查网络连接和数据源")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
