#!/usr/bin/env python3
"""
关键测试：验证真实数据采集
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

def test_direct_collection():
    """直接测试数据采集"""
    print("=== 关键测试：直接数据采集 ===")
    
    try:
        from data.collector import LotteryDataCollector
        
        # 创建采集器
        collector = LotteryDataCollector()
        print("✅ 采集器创建成功")
        
        # 直接测试HTML源
        url = 'https://www.17500.cn/chart/3d-tjb.html'
        print(f"测试URL: {url}")
        
        data = collector.collect_from_html_source(url)
        print(f"采集结果: {len(data)} 条数据")
        
        if data:
            print("\n📊 采集到的数据样例:")
            for i, record in enumerate(data[:5]):
                issue = record.get('issue', 'N/A')
                date = record.get('draw_date', 'N/A')
                hundreds = record.get('hundreds', 'N/A')
                tens = record.get('tens', 'N/A')
                units = record.get('units', 'N/A')
                
                print(f"  {i+1}. 期号: {issue}, 日期: {date}, 号码: {hundreds}{tens}{units}")
                
                # 验证数据真实性
                if str(issue).startswith(('2024', '2025')) and len(str(issue)) == 7:
                    print(f"    ✅ 真实期号格式: {issue}")
                else:
                    print(f"    ❌ 期号格式异常: {issue}")
            
            return True
        else:
            print("❌ 未采集到任何数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_collection_stats():
    """测试采集统计"""
    print("\n=== 采集统计测试 ===")
    
    try:
        from data.collector import LotteryDataCollector
        
        collector = LotteryDataCollector()
        
        # 执行采集
        data = collector.collect_data(limit=5)
        
        # 获取统计信息
        stats = collector.collection_stats
        print(f"统计信息:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功请求: {stats['successful_requests']}")
        print(f"  失败请求: {stats['failed_requests']}")
        print(f"  总记录数: {stats['total_records']}")
        
        if data and len(data) > 0:
            print(f"✅ 成功采集 {len(data)} 条数据")
            return True
        else:
            print("❌ 采集失败")
            return False
            
    except Exception as e:
        print(f"❌ 统计测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始关键测试...")
    
    # 测试1：直接采集
    test1_ok = test_direct_collection()
    
    # 测试2：采集统计
    test2_ok = test_collection_stats()
    
    print("\n" + "="*50)
    print("关键测试结果:")
    print(f"  直接采集: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  采集统计: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 关键测试全部通过！")
        print("✅ 确认能够采集到真实的福彩3D数据")
        return True
    else:
        print("\n❌ 关键测试存在问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
